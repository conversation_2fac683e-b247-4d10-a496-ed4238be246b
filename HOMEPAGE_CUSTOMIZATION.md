# 首页定制指南

## 概述

你的博客首页已经成功改造为类似 [satnaing.dev](https://satnaing.dev/) 的现代化个人作品集风格。新首页包含以下几个主要部分：

1. **Hero 区域** - 个人介绍和主要信息
2. **About 区域** - 详细的个人背景和教育经历
3. **Projects 区域** - 项目作品展示
4. **Blog 区域** - 最新博客文章预览
5. **Contact 区域** - 联系方式和社交链接

## 如何个性化你的网站

### 1. 更新个人信息

编辑 `src/data/personal.ts` 文件来自定义你的个人信息：

```typescript
export const PERSONAL_INFO = {
  // 基本信息
  name: "你的姓名",
  title: "你的职位/标题",
  email: "<EMAIL>",
  location: "你的城市, 国家",
  
  // Hero 区域
  hero: {
    greeting: "Hi my name is",
    taglines: [
      "PASSIONATE PROGRAMMER",
      "FREELANCER", 
      "FULL-STACK DEVELOPER"
    ],
    description: "你的个人介绍...",
    // ...
  },
  // ...
}
```

### 2. 更新项目信息

编辑 `src/data/projects.ts` 文件来添加你的项目：

```typescript
export const projects: Project[] = [
  {
    title: "项目名称",
    description: "项目描述",
    technologies: ["React", "TypeScript", "Node.js"],
    githubUrl: "https://github.com/username/repo",
    liveUrl: "https://your-project.com",
    featured: true, // 是否在首页展示
    category: "Web Development"
  },
  // 添加更多项目...
];
```

### 3. 更新网站配置

编辑 `src/config.ts` 文件来更新网站的基本配置：

```typescript
export const SITE = {
  website: "https://your-domain.com",
  author: "你的姓名",
  profile: "https://your-profile.com",
  desc: "你的网站描述",
  title: "你的网站标题",
  // ...
};
```

### 4. 更新社交链接

编辑 `src/constants.ts` 文件来更新你的社交媒体链接。

## 新增的组件

### Hero.astro
- 主要的英雄区域组件
- 包含个人介绍、标签和行动按钮
- 支持平滑滚动到其他区域

### About.astro
- 个人背景介绍组件
- 包含教育经历和技能展示
- 使用个人信息数据动态渲染

### Projects.astro
- 项目展示组件
- 支持特色项目和普通项目分类
- 包含 GitHub 和在线演示链接

### BlogPreview.astro
- 博客文章预览组件
- 显示最新的 4 篇博客文章
- 包含查看所有文章的链接

### Contact.astro
- 联系方式组件
- 包含邮件联系和社交媒体链接
- 展示专业特性

## 样式改进

### 新增的 CSS 类
- `.container` - 响应式容器
- `.gradient-text` - 渐变文字效果
- `.animate-fade-in-up` - 淡入向上动画
- `.focus-ring` - 改进的焦点样式

### 响应式设计
- 所有组件都支持移动端和桌面端
- 使用 Tailwind CSS 的响应式断点
- 优化的触摸交互

## 性能优化

- 使用 Astro 的静态生成
- 优化的图片占位符
- 平滑滚动和过渡动画
- 最小化的 JavaScript

## 可访问性

- 语义化的 HTML 结构
- 适当的 ARIA 标签
- 键盘导航支持
- 高对比度的颜色方案

## 下一步

1. **添加真实的项目图片**：替换项目展示中的占位符图片
2. **个性化头像**：在 About 区域添加你的真实头像
3. **SEO 优化**：更新 meta 标签和 Open Graph 信息
4. **添加更多项目**：在 `projects.ts` 中添加更多项目
5. **自定义主题**：根据个人喜好调整颜色和字体

## 技术栈

- **Astro** - 静态站点生成器
- **React** - 组件库（用于交互组件）
- **TypeScript** - 类型安全
- **Tailwind CSS** - 样式框架
- **Markdown** - 内容管理

## 支持

如果你在定制过程中遇到问题，可以：

1. 查看 Astro 官方文档
2. 检查浏览器控制台的错误信息
3. 确保所有依赖都已正确安装
4. 验证配置文件的语法

祝你使用愉快！🚀
