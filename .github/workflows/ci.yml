name: CI

permissions:
  contents: read

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize
      - reopened
  workflow_call:

jobs:
  build:
    name: Code standards & build
    runs-on: ubuntu-latest
    timeout-minutes: 3

    strategy:
      matrix:
        node-version: [20]

    steps:
      - name: "☁️ Checkout repository"
        uses: actions/checkout@v4

      - name: "📦 Install pnpm"
        uses: pnpm/action-setup@v4
        with:
          version: 10.11.1

      - name: Use Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "pnpm"

      - name: "📦 Install dependencies"
        run: pnpm install

      - name: "🔎 Lint code"
        run: pnpm run lint

      - name: "📝 Checking code format"
        run: pnpm run format:check

      - name: "🚀 Build the project"
        run: pnpm run build
