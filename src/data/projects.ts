export interface Project {
  title: string;
  description: string;
  image?: string;
  technologies: string[];
  githubUrl?: string;
  liveUrl?: string;
  featured?: boolean;
  category?: string;
}

export const projects: Project[] = [
  {
    title: "AstroPaper Blog Theme",
    description: "A minimal, accessible and SEO-friendly Astro blog theme. One of the most starred blog templates built with Astro. Features dark/light mode, responsive design, and excellent performance.",
    technologies: ["Astro", "TypeScript", "React", "TailwindCSS"],
    githubUrl: "https://github.com/satnaing/astro-paper",
    liveUrl: "https://astro-paper.pages.dev/",
    featured: true,
    category: "Web Development"
  },
  {
    title: "Terminal Portfolio",
    description: "My portfolio website in terminal version developed with React and TypeScript. Features command-line interface, autocomplete, multiple themes, and interactive commands.",
    technologies: ["React", "TypeScript", "Styled-Components"],
    githubUrl: "https://github.com/satnaing/terminal-portfolio",
    liveUrl: "https://terminal.satnaing.dev/",
    featured: true,
    category: "Web Development"
  },
  {
    title: "E-commerce Fashion Store",
    description: "An ecommerce web application where users can browse various products, add to wishlist, add to cart, and make purchase. Available in multiple languages with modern UI/UX.",
    technologies: ["NextJS", "TypeScript", "TailwindCSS", "ContextAPI"],
    githubUrl: "https://github.com/satnaing/haru-fashion",
    liveUrl: "https://haru-fashion.vercel.app/",
    featured: true,
    category: "Full-stack"
  },
  {
    title: "RESTful API Service",
    description: "A comprehensive RESTful API developed for ecommerce projects. Includes CRUD operations, authentication, authorization, forgot/reset password functionality and full-text search.",
    technologies: ["ExpressJS", "TypeScript", "PostgreSQL", "Prisma"],
    githubUrl: "https://github.com/satnaing/haru-api",
    liveUrl: "https://satnaing.github.io/haru-api/",
    featured: false,
    category: "Backend"
  },
  {
    title: "Next.js Bookstore",
    description: "An online bookstore developed using NextJS 13 with appDir and StrapiCMS. Features modern design, server-side rendering, and content management system integration.",
    technologies: ["NextJS", "Radix UI", "TailwindCSS", "TanstackQuery", "StrapiCMS"],
    githubUrl: "https://github.com/satnaing/next-bookstore",
    liveUrl: "https://nextbookstore.vercel.app/",
    featured: false,
    category: "Full-stack"
  },
  {
    title: "Shadcn Admin Dashboard",
    description: "Admin Dashboard UI built with Shadcn and Vite. Built with responsiveness and accessibility in mind. Features modern components and clean design patterns.",
    technologies: ["ShadcnUI", "Vite", "React Router", "TypeScript"],
    githubUrl: "https://github.com/satnaing/shadcn-admin",
    liveUrl: "https://shadcn-admin.netlify.app/",
    featured: false,
    category: "Frontend"
  },
  {
    title: "React Native Mobile App",
    description: "Cross-platform mobile application built with React Native. Features native performance, modern UI design, and seamless user experience across iOS and Android.",
    technologies: ["React Native", "TypeScript", "Expo", "Redux"],
    githubUrl: "#",
    liveUrl: "#",
    featured: false,
    category: "Mobile"
  },
  {
    title: "Vue.js SPA",
    description: "Single Page Application built with Vue.js 3 and Composition API. Features modern state management, routing, and component architecture.",
    technologies: ["Vue.js", "TypeScript", "Pinia", "Vue Router"],
    githubUrl: "#",
    liveUrl: "#",
    featured: false,
    category: "Frontend"
  },
  {
    title: "Python Data Analysis Tool",
    description: "Data analysis and visualization tool built with Python. Features data processing, statistical analysis, and interactive visualizations.",
    technologies: ["Python", "Pandas", "NumPy", "Matplotlib", "Streamlit"],
    githubUrl: "#",
    liveUrl: "#",
    featured: false,
    category: "Data Science"
  }
];

export const getFeaturedProjects = (): Project[] => {
  return projects.filter(project => project.featured);
};

export const getProjectsByCategory = (category: string): Project[] => {
  return projects.filter(project => project.category === category);
};

export const getAllCategories = (): string[] => {
  const categories = projects.map(project => project.category).filter(Boolean);
  return [...new Set(categories)];
};
