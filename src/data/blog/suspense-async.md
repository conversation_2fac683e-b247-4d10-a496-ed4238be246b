---
author: silkshadow
pubDatetime: 2022-10-07T15:20:35Z
modDatetime: 2022-10-07T16:46:34.155Z
title: Suspense异步获取数据
featured: false
draft: false
tags:

- vscode
description:
  Suspense异步获取数据
---

早在2018年，“Suspense”作为React 16.6版本的一个实验性功能而发布，它的主要目标是结合`React.lazy`处理代码分割。是[前置仓首屏资源优化](https://xcz.yuque.com/iqloyo/b-reports/qvmpvg#HKKBu)手段之一：

```jsx
// WorkflowGraph首页基于BizCharts.js（1.43MB）封装的组件
const WorkflowGraph = React.lazy(() => import('./WorkflowGraph'));

<Suspense fallback={<>Loading...</>}>
  <WorkflowGraph dataSource={processXML} />
</Suspense>
```



Suspense除了可以等待组件，还允许等待其它任何东西：图片、脚本、或其他异步的工作。本文主要讲通过等待数据获取来改善用户体验。



先来了解下不使用Suspense的流程问题与局限性：

<h3 id="lRGgY"><font style="color:rgb(0, 0, 0);">Fetch-on-Render —— </font>阻塞彼此数据流</h3>
```jsx
function ComponentA() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetchAwesomeData().then(data => setData(data));
  }, []);
  
  return data === null ? <p>Loading data...</p> : <ComponentB />;
}

function ComponentB() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetchGreatData().then(data => setData(data));
  }, []);
  
  return data === null ? <p>Loading data...</p> : <SomeComponent data={data} />;
}
```

运行此代码整体流程：

1. 渲染ComponentA组件
2. fetchAwesomeData，等待结果
3. 渲染ComponentB组件
4. fetchGreatData，等待结果
5. 渲染SomeComponent组件



由于组件之间的数据获取是按顺序进行的，这意味着它们会互相阻塞。

<h3 id="fo1As">Fetch-then-render —— 等待所有数据请求</h3>
未来解决这个问题，我们尽可能早地请求数据：

```jsx
function fetchAllData() {
  return Promise.all([
		fetchAwesomeData(),
		fetchGreatData()
  ]).then(([awesomeData, greatData]) => ({
    awesomeData,
		greatData
  }))
}

function ComponentA() {
  const [awesomeData, setAwesomeData] = useState(null);
  const [greatData, setGreatData] = useState(null);

  useEffect(() => {
    fetchAllData().then(({ awesomeData, greatData }) => {
      setAwesomeData(awesomeData);
      setGreatData(greatData);
    });
  }, []);

 return data === null ? <p>Loading data...</p> : <ComponentB />;
}

function ComponentB({data}) {
  return data === null ? <p>Loading data...</p> : <SomeComponent data={data} />;
}
```

运行此代码整体流程：

1. 渲染ComponentA组件
2. 同时获取数据：fetchAwesomeData，fetchGreatData，等待结果
3. 渲染ComponentB组件、渲染SomeComponent组件



这解决了组件顺序阻塞彼此数据流的问题。但是，它引入了另一个问题 —— 我们必须等待所有数据请求完成后才能为用户呈现内容。可想而知，这并不是一种最佳体验。

其实，这也是我们日常开发使用的方式。



从本质上讲，“fetch-on-render”和“fetch-then-render”的主要问题归结为这样一个事实：我们试图强制同步两个不同的流，即数据获取流和React生命周期。

<h3 id="thmOU">render-as-you-fetch —— 即取即呈现</h3>
```jsx
const specialSuspenseResource = fetchAllDataSuspense();

function App() {
  return (
    <Suspense fallback={<h1>Loading data...</h1>}>
      <ComponentA />
      <Suspense fallback={<h2>Loading data...</h2>}>
        <ComponentB />
      </Suspense>
    </Suspense>
  );
}

function ComponentA() {
  const data = specialSuspenseResource.awesomeData.read();
  return <h1>{data.title}</h1>;
}

function ComponentB() {
	const data = specialSuspenseResource.greatData.read();
  return <SomeComponent data={data} />;
}
```



<h3 id="tlxR5">实践</h3>
> —— 在前置仓工作台系统中的体验交互
>



获取用户信息接口是一进入页面就要调用的，用于右上角展示用户信息、埋点初始化、添加水印等。但是在用户未登录的清空下，接口返回错误，进入系统后首先展示一个空的框架及错误提示后返回登录页，用户体验较差：

![](https://cdn.nlark.com/yuque/0/2022/png/112238/1662012855625-f0832b60-84f0-4ae8-81f4-c6a5a001bf92.png)



优化前，代码是直接导出框架视图组件：

```jsx
function BasicLayout(props: BasicLayoutProps) {
  // 
}

export default connect(mapStateToProps, mapDispatchToProps)(BasicLayout);
```



修改后，将获取用户接口改造为通过Suspense即时请求，通过fallback展示加载loading状态：

```jsx
const Layout = connect(mapStateToProps, mapDispatchToProps)(BasicLayout);

const Index = props => {
  const [userReader] = useAsyncResource(getLayoutUserInfo, []);
  // render-as-you-fetch 
  const userInfo = userReader();

  if (userInfo.code) {
    setTimeout(() => {
      clearSessionCookies(true);
      window.location.href = `${BASE.login_url}?redirectUrl=${encodeURIComponent(window.location.href)}`;
    }, 300);
    // 如果返回异常，展示返回登录页
    return <PageLoading tip={userInfo.msg + '，正在返回登录页'} />;
  }
  return <Layout {...props} userInfo={userInfo} />;
};

const Main = props => {
  return (
    <Suspense fallback={<PageLoading />}>
      <Index {...props} />
    </Suspense>
  );
};
export default Main;
```

登录异常展示效果：

![](https://cdn.nlark.com/yuque/0/2022/png/112238/1662012935404-9f2f5c52-3553-4215-986f-064ce688c9fc.png)



**除了在用户体验上的优势，对于前端开发者而言，也减少了异步默认值处理的心理负担。**一种很常见的情况，在一些带有仓库查询界面中，需要默认查询该用户归属仓库下的结果，开发的棘手点在于：

1. **值受控：**仓库是异步获取，那么就不能使用defalutValue设置默认值，而只能使用value受控。
2. **调用顺序：**进入系统默认查询，需要在获取用户信息之后，而获取用户信息的调用在全局，查询的调用在当前页面。

但是使用Suspense异步获取后，进入系统能保证已经获取到用户信息，无论是调用顺序还是异步默认值，已然无后顾之忧。



同时，对于后端而已，也在一定程度上减少了一些服务器压力。优化前，假设用户**当日首次（未登录情况）**进入访问最多的销售开单页面，会请求将近20多个接口（包括框架以及首次弹框请求），而优化后，通过获取用户接口这道门槛，可以减少了大量不必要的接口。



当然，这个方案也有缺陷，如果用户已经登录了刷新系统，用户可用时间需增加等待用户接口请求时长。



<h4 id="iaNyy">小结</h4>
|  | 优化前 | 优化后 |
| --- | --- | --- |
| 用户体验 | 页面体感较差 | 自定义异常登录界面，界面更友好 |
| 前端开发 | 需判断调用顺序，考虑异步默认值清空 | 无此心理负担，降低此类代码错误率 |
| 登录异常场景服务器压力 | 登录异常情况访问大量不必要接口 | 登录异常情况仅访问用户信息接口 |
| 已登录用户可交互时间 | 资源加载时间+渲染时间 | 资源加载时间+用户接口请求时长-渲染时间 |
















































