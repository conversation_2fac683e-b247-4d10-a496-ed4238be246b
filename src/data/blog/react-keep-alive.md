---
author: silkshadow
pubDatetime: 2020-12-14T15:20:35Z
modDatetime: 2020-12-14T16:46:34.155Z
title: 在现有react框架中接入keep-alive
featured: false
draft: false
tags:

- color-schemes
- docs
description:
  How you can enable/disable light & dark mode; and customize color schemes
  of AstroPaper theme.
---

## ❓困境
目前接入权限系统的框架，都使用了 @ncz/work 框架，它封装了很多布局层面的功能，我们可以只关注业务页面的开发。

界面通常长这样：

![](https://cdn.nlark.com/yuque/0/2020/png/112238/1607866653769-be00ca5e-8097-44bc-9dd6-585911b16569.png)

我们可以切换tab，切换已经打开过的界面，就像切换浏览器标签页那样。但是当我们切换tab的时候，如果不做额外的处理，每次切回tab，都会重新加载页面，这用户体验似乎不太友好，这个tab只是摆设吗？

如果页面数据、页面状态等存储到model层的state中，似乎可以解决以上问题，但这是不是太费劲了，我表示拒绝o(￣ヘ￣o＃)。



## 💡 方案
我在寻求类似vue keep-alive的方案，找了些插件，也踩了一些坑，这些坑坑洼洼的路就不再赘述，总而言之，我找到了[react-activation](https://github.com/CJY0208/react-activation)，它解决了我的问题。

### 缓存
首先我阅读了文档，了解了它的使用方法：

1. 在应用入口，使用 `<AliveScope>` ，这样它不会被卸载。
2. 在需要缓存状态的页面组件里，包裹 `<KeepAlive>`。

![](https://cdn.nlark.com/yuque/0/2020/png/112238/1607867835267-ca4d2724-83c1-4dca-814d-bc610c5376f6.png)

就这么简单，页面状态就被缓存下来了，我再也不用定义**好多好多变量**到model的state中了。

### 卸载
哎呀，刚刚忘记画了，layout的每个tab上，都有一个关闭按钮，当我关闭按钮，下次进来，要是全新的页面哦（向浏览器交互看齐）。

![](https://cdn.nlark.com/yuque/0/2020/png/112238/1607868675883-c3fc415d-9d58-4cb0-80cf-4498eaa5dfe5.png)

我再次阅读了文档，组件支持缓存控制，它支持两种方案控制：

1. 在 `KeepAlive` 添加 `when` 属性控制
2. 在 `KeepAlive` 添加 `name` 属性标识，通过方法 `drop` 、`dropScope` 、`clear` 等方法移除。

很好很强大(*￣︶￣)。

### 生命周期
通常，我们会缓存一个列表页，点击列表页，会进入详情页，详情页可能会有一些操作，这会导致一些状态的变更。

但由于我们缓存了列表页，页面不再刷新。所以要解决这样的场景，就是建立一个新的生命周期，每次我再次进入缓存页面时，触发一个特定的生命周期。



![](https://cdn.nlark.com/yuque/0/2020/png/112238/1607869417551-990f2289-68cc-4384-a54d-019b7a842d1b.png)



在 react-activation 中，缓存后的组件，再次进入会触发 `componentDidActivate` ，缓存状态中离开，会触发`componentWillUnactivate`。

对应函数组件的钩子，分别是 `useActivate`，`useUnactivate`。



## 📝 解决
通过调研， react-activation 足够解决我们的困境。现在，我要对work框架进行改造。

### 安装
```bash
npm i react-activation --save
```

### 配置编译
> `.babelrc` 添加插件
>

```json
{
  "plugins": [
    // ...
    "react-activation/babel"
  ]
}
```

### LayoutRoute 组件修改
我们已经知道，在入口文件处，添加 `AliveScope` 包裹，在要缓存的页面，使用 `KeepAlive `包裹。

所以我们在 LayoutRoute.js 做如下改动点：

+ line 7: 引入 `AliveScope`
+ line 37: 使用 `AliveScope` 标签包裹页面



因为 work 框架不提供，关闭tab标签事件，但是提供当前所有的标签数组 `tabPanes` ，我们可以通过这个数组的比较，得知关闭的tab标签页。所以这个页面额外添加两个改动：

+ line 11: tabPanes 解构
+ line 30: tabPanes 属性值透传至 GlobalState

```jsx
// line 7: 引入AliveScope
import { AliveScope } from 'react-activation';

class LayoutRoute extends React.Component {
  onSuccessLoad = successData => {
    // line 11: tabPanes 解构
    const { userMsg, tabPanes, systemList, systemSelected, menuBtnList, sliderList, allMenuBtnList } = successData;
    const { currentLedger } = userMsg;
    const { roleDisplayRoList } = userMsg;
    this.props.dispatch({
      type: 'GlobalState/updateState',
      payload: {
        // ...
        // line 30: tabPanes 属性值透传至 GlobalState
        tabPanes,
      },
    });
  };

  render() {
    const { systemName, openLocalMenu, loginUrl, env } = window.conf;
    return (
      // line 37: 使用 AliveScope 标签包裹页面
      <AliveScope>
        <Route
          // ....
        />
      </AliveScope>
    );
  }
}
```

### 创建 KeepAlive组件
我想通过一个公共的组件，在需要缓存的页面中，只要包裹这个组件，就实现缓存，不需要关心额外的东西。



以下是我的思路：

+ 为每个页面创建唯一的key值，key值与tabPanes有一定的关系
+ 比较每次tabPanes变动值，减少的tab卸载缓存



key 值使用的是完整路由，对应tabPanes的linkKey值：

```jsx
let cacheKey = `${props.location.pathname}${props.location.search}`;
```



usePrevious 是保存上一次tabPanes的hook 方法：

```jsx
// 上一次
const prevCacheKeys = usePrevious(cacheKeys);
```

通过 loadsh 的 difference 工具方法，返回tabPanes变动值，针对变动的值，卸载缓存（KeepAliveInner组件的作用）：

```jsx
difference(prevCacheKeys, cacheKeys).forEach((key) => {
  refresh(key);
  drop(key);
});
```



完整代码：

```jsx
// @filename: components/KeepAlive/index.js

import React, { useRef, useEffect } from 'react';
import KeepAlive, { useActivate, useAliveController } from 'react-activation';
import { connect } from 'dva';
import { difference } from 'loadsh';

function usePrevious(value) {
  const ref = useRef();
  useEffect(() => {
    ref.current = value;
  }, [value]);

  return ref.current;
}

const KeepAliveInner = connect(({ GlobalState }) => {
  const { tabPanes = [] } = GlobalState;
  return {
    cacheKeys: tabPanes.map((tab) => tab.linkKey),
  };
})((props) => {
  const { drop, refresh } = useAliveController();
  const { cacheKeys } = props;
  const prevCacheKeys = usePrevious(cacheKeys);

  useEffect(() => {
    difference(prevCacheKeys, cacheKeys).forEach((key) => {
      refresh(key);
      drop(key);
    });
  }, [cacheKeys]);

  return props.render();
});

export default (PageComponent) => {
  return ({ ...props }) => {
    let cacheKey = `${props.location.pathname}${props.location.search}`;
    return (
      <KeepAlive name={cacheKey}>
        <KeepAliveInner {...props} cacheKey={cacheKey} render={() => <PageComponent {...props} />} />
      </KeepAlive>
    );
  };
};
```



### 使用
+ 只需在页面外使用自定义组件KeepAlive包裹，便可实现缓存。
+ 通过 useActivate ，为每次切换进入页面刷新界面数据。

```jsx
import KeepAlive from '@components/KeepAlive';
import { useActivate, useUnactivate } from 'react-activation';

const PageComponent = props => {
	useActivate(() => {
    refreshList(); // 每次进入页面，刷新列表
  });
  return <div>缓存页面</div>
}

export default connect(
  mapStateToProps,
  mapDispatchToProps
)(
  KeepAlive(PageComponent)
);
```



完成~



