---
author: silkshadow
pubDatetime: 2022-06-07T15:20:35Z
modDatetime: 2022-06-07T16:46:34.155Z
title: webpack5升级实践
featured: false
draft: false
tags:
- webpack5
description:
  本文为经过几天的踩坑与研究，总结出 work框架(公司自研框架，类似umi)的webpack配置升级为webpack5，并对编译进行优化的实践~
---

本文为经过几天的踩坑与研究，总结出 work框架(公司自研框架，类似umi)的webpack配置升级为webpack5，并对编译进行优化的实践~

## 插件升级
既然是从webpack4升级到webpack5，我们第一时间可能先会想到直接把webpack、webpack-cli升级到最新的版本不就好了。但是如果只是单纯的这样手动的去升级包的话，很快就会遇到各种各样的loader跟plugin语法报错。

所以为了减少升级的成本，不建议通过手动去升级包的版本，而使用一键升级包的工具 ——  **npm-check-updates**：

```bash
# 安装
npm install -g npm-check-updates

# 在package.json的同级目录下更新package.json文件
ncu -u

# 安装新包
yarn
```

当然我们在执行完 `ncu - u` 后，<font style="color:#E8323C;">检查下是否都有必要升级</font>，比如最严重的就是react直接升级到了18，我采取的方案是：编译相关的插件全部升级，其他暂不升级。

## 配置调整
升级完之后，直接运行，会发现有报错，webpack5的配置方式还是有较多差异的。这边通过尝试与信息查阅，调整好了work框架的配置信息，主要的配置变更可以分为以下几种：



### plugin配置升级
1. **插件函数导出方式变更**

```javascript
// webpack 4
const merge = require('webpack-merge');

// webpack 5
const { merge } = require("webpack-merge");
```

```javascript
// webpack 4
const ManifestPlugin = require('webpack-manifest-plugin');

// webpack 5
const { WebpackManifestPlugin: ManifestPlugin } = require('webpack-manifest-plugin');
```



2. **插件变为内置**

CleanWebpackPlugin从插件变成[内置](https://webpack.js.org/configuration/output/#outputclean)，并可以选择需要保留的目录

```javascript
// webpack 4
new CleanWebpackPlugin(["dist"]),
   
// webpack 5
output: {
   clean: true, // 在生成文件之前清空 output 目录
   keep: /ignored\/dir\//, // Keep these assets under 'ignored/dir'.
},
```

### loader配置升级
1. **不再支持内联方式配置属性**

```javascript
// webpack 4
{
  // ...
  loader: 'babel-loader?cacheDirectory',
}

// webpack 5
{
   // ...
   loader: 'babel-loader',
   options: {
    cacheDirectory: true,
   }
 },
```

```javascript
// webpack 4
{
  // ...
  use: ['style-loader', 'css-loader?modules', 'sass-loader'],
}

// webpack 5
{
  // ...
  use: [
    'style-loader', 
    {
      loader: "css-loader",
      options: {
        modules: true,
      },
    },
    'sass-loader'
  ],
},
```



2. **内置静态资源构建能力 —— Asset Modules**

静态资源的处理，包括图片、字体、视频、音频等内容，webpack5之后都不用写单独的loader了。

```javascript
{
  test: /\.(ttf|eot|svg|woff|woff2|png|svg|jpg|gif)$/,
    type: 'asset',
      parser: {
        dataUrlCondition: {
          maxSize: 8192
        }
      }
},
```

### devServer配置升级
```javascript
// 配置项 disableHostCheck 改为 allowedHosts
devServer: {
  disableHostCheck: true,
}

devServer: {
  allowedHosts: "all",
}


// overlay 调整
devServer: {
  overlay: {
    errors: true,
  },
}

devServer: {
  client: {
    overlay: true,
  },
}

// hot 默认为true可移除
```

[  
](https://webpack.docschina.org/configuration/optimization/#optimizationsplitchunks)

### 其他升级改造点
具体查看[迁移指引](https://webpack.docschina.org/migrate/5/)。

**清理配置：**

+ 如果使用了类似于 node.fs: 'empty'，请使用 resolve.fallback.fs: false 代替

**重新考虑 **`**optimization.splitChunks**`** 的配置：**

+ optimization.splitChunks.cacheGroups.vendors → optimization.splitChunks.cacheGroups.defaultVendors
+ 当使用自定义配置时，请删除 name: false，并将 name: string | function 替换为 idHint: string | function

**考虑移除的默认值：**

+ 当设置 `entry: './src/index.js'` 时，可以省略它，此为默认值。
+ 当设置 `output.path: path.resolve(__dirname, 'dist')` 时，可以省略它，此为默认值。
+ 当设置 `output.filename: '[name].js'` 时：可以省略它，此为默认值。



### 构建效率对比
对比数据为我的电脑构建效果：

| 对比 | webpack 4 | | webpack 5 |
| --- | --- | --- | --- |
| 开发编译 | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1654591536083-ef3ba86f-3c56-4b18-b62e-a0da6b8199b6.png) | 60s | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1654592751761-e89a02a6-73c6-4485-97c7-9c3d408aa54c.png) | 28s |
| 热更新 | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1653966073987-6838264a-fe26-489f-9d0d-be44732bc9be.png) | 5~9s | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1654591632867-76c8f2e0-9dad-4ce3-bd15-96830856b0fa.png) | 1~2s |
| 打包构建 | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1654591183815-fbbbe8a9-018d-465b-9f44-aa43827390b9.png) | 113s | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1654591589839-1482b3e2-7ff6-46f4-b65d-342b6eb012d0.png) | 60s |


## 编译优化
### 使用esbuild打包
esbuild 是一个 JavaScript Bundler 打包和压缩工具。

:::warning
esbuild 了解更多可关注学习小组 0609期主题。

:::



`[esbuild-loader](https://github.com/privatenumber/esbuild-loader)` 是基于esbuild 的webpack loader和plugin。



```javascript
{
  test: /\.tsx?$/,
  include: [path.resolve(__dirname, 'src')],
  exclude: /node_modules/,
  use: [{
       loader: 'esbuild-loader',
       options: {
         loader: 'tsx',
         target: 'es2015'
       },
     }],
},
{
  test: /\.js$/,
  include: [path.resolve(__dirname, 'src')],
  exclude: /node_modules/,
  use: [{
      loader: 'esbuild-loader',
      options: {
        loader: 'jsx', 
        target: 'es2015'
      },
    }],
},
```

```javascript
const { ESBuildMinifyPlugin } = require('esbuild-loader')

module.exports = {
  ...,

  optimization: {
    minimizer: [
      new ESBuildMinifyPlugin({
        css: true
      })
    ]
  },
}
```

#### 构建效率对比
| 对比 | webpack 5 | Webpack 5 + esbuild |
| --- | --- | --- |
| 开发编译 | 28s | 13~14s |
| 热更新 | 1~2s | 1s左右 |
| 打包构建 | 60s | 14.7s |


### 持久化缓存
```javascript
cache: {
  type: 'filesystem',
    buildDependencies: {
      config: [__filename],
    },
},
```

#### 构建效率对比
| 配置 | 初次构建 | 二次构建 |
| --- | --- | --- |
| no-filesystem | 14.67s | 14.504s |
| filesystem | 14.298s | 3.696s |


## 最终结果对比
| 对比 | 本地构建 | 效能构建日志 | 效能构建时长 |
| --- | --- | --- | --- |
| 优化前 | 113s | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1654593420825-f7aa9f3a-bc2c-43dc-b49e-de33fdb6715b.png) | 3min |
| 优化后 | 14s | ![](https://cdn.nlark.com/yuque/0/2022/png/112238/1654593449813-3a612487-3135-4b7a-a02d-6abee4fbd32d.png) | 2.3min |


---

参考：

+ [Webpack5 新特性业务落地实战](https://zhuanlan.zhihu.com/p/348612482)
+ [记录将项目中webpack4升级至webpack5一次成功尝试](https://www.mybj123.com/11258.html)

