---
author: silkshadow
pubDatetime: 2021-06-15T15:20:35Z
modDatetime: 2021-06-15T16:46:34.155Z
title: 职责链模式处理弹框链
featured: false
draft: false
tags:
  - 设计模式
  - 业务沉淀
description:
  How you can enable/disable light & dark mode; and customize color schemes
  of AstroPaper theme.
---

前置仓销售开单加入清单操作，一开始的需求仅仅是做了一些开单的前置校验，随着需求的迭代，加入清单有越来越多的前置判断与二次操作，到1.5迭代时，加入清单的前置交互流程大致可以分为：

+ 效期检测：是否选择了效期
+ 重复检测：购物清单是否已存在
+ 赠品检测：是否可能设置为赠品
+ 库存检测：库存是否充足

实际流程中又有一些子流程的逻辑，想具体了解可以看整理的流程图[《商品加入销售清单流程梳理》](<https://xcz.yuque.com/docs/share/11c98f8c-69c4-49eb-8be1-dd23ed7a592f?#> 《商品加入销售清单流程梳理》)。

本文为了说明整体思路，将需求逻辑简化：

1. 重复检测：

购物清单是否已经存在该商品，如果已存在，弹框展示 “该商品已加入销售清单，需要覆盖还是叠加？”：

    - 点击“覆盖”按钮：以【最新的数量】加入清单
    - 点击“叠加”按钮：以【原来的数量+新输入的数量】加入清单
2. 赠品检测：

价格是否为0.01，如果为0.01，弹框展示 “系统检测到商品价格为0.01元，您是想作为赠品进行处理吗？”：

    - 点击“确定”按钮：作为【赠品】加入清单
    - 点击“取消”按钮：作为【普通商品】加入清单
3. 库存检测：

库存是否充足，如果库存不足，弹框提示“当前SKU库存不足，是否确认加入清单？"：

    - 点击“确定”按钮：加入清单
    - 点击“取消”按钮：不加入清单

最先开始的处理逻辑可能会有点像这样：

```javascript
function addToCart(props) {
  const { record } = props;
  /**
   * 库存校验
   */
  const stockDetection = record => {
    const { stock, amount } = record;
    if (stock < amount) {
      Dialog.confirm({
        content: `当前SKU库存不足，是否确认加入清单？`,
        onOk: () => {}, // 添加到购物清单
      });
    } else {
      // 添加到购物清单
    }
  };

  /**
   * 赠品校验
   */
  const giftDetection = record => {
    const { salePrice } = record;
    if (parseFloat(salePrice) <= 0.01) {
      Dialog.confirm({
        content: `系统检测到商品价格为0.01元，您是想作为赠品进行处理吗？`,
        onOk: () => {
          const _record = { ...record };
          _record.giftFlag = true;
          stockDetection(_record);
        },
        onCancel: () => stockDetection(record),
      });
    } else {
      stockDetection(record);
    }
  };

  /**
   * 重复校验
   */
  const duplicateDetection = record => {
    const { orderList } = props;
    const { kzSkuCode } = record;
    const exist = orderList.find(item => item.kzSkuCode === kzSkuCode);
    if (exist) {
      Dialog.confirm({
        content: '该商品已加入销售清单，需要覆盖还是叠加？',
        okProps: { children: '覆盖' },
        cancelProps: { children: '叠加', type: 'primary' },
        onOk: () => {
          giftDetection(record);
        },
        onCancel: () => {
          const _record = { ...record };
          _record.amount = exist.amount + _record.amount;
          giftDetection(_record);
        },
      });
    } else {
      giftDetection(record);
    }
  };

  duplicateDetection(record);
}
```

该段代码大致的流程是：先调用重复检测`duplicateDetection`方法，相应操作与处理后，在**回调**中调用赠品检测`giftDetection`方法，相应操作与处理后，在**回调**中调用库存检测`stockDetection`方法，最终在确定按钮的回调中，将商品加入清单。

这样的方式显然不好维护，在真实业务逻辑中，更是难以梳理子系统逻辑。

所以我考虑用一种职责链的链式调用方式来处理：__

<font style="color:#000000;"></font>

```javascript
step1()
  .then(step2)
  .then(step3)
  .then(function(){
    console.log('done')
  })
  .catch(function(){
    console.log('failed')
  })
```

将弹框回调转为链式调用的精髓，其实就是将回调变为promise：![](https://cdn.nlark.com/yuque/0/2021/png/112238/1623650072237-9594b90c-5854-4fc0-af2a-90f3f21ae057.png)

```javascript
function step() {
  return new Promise((resolve, reject) => {
      if (condition) {
        Dialog.confirm({
          content: `xxxxxxxxxxxx`,
          onOk: resolve,
          onCancel: reject,
        });
      } else {
        resolve();
      }
    });
}
```

多个任务，形成一条 **Dialog Promises Chain**，进入下一步流程，使用 resolve，退出流程，使用 reject。

![](https://cdn.nlark.com/yuque/0/2021/png/112238/1623744924308-126f137c-163a-4670-be2d-26962010cbfb.png)

所以，上述添加到销售清单的方法，可以这样改造：

```javascript
class AddToCart {
  constructor(options) {
    this.orderList = options.orderList;
    this.record = { ...options.record };
  }
  validate() {
    return this.duplicateDetection(this.record)
      .then(record => {
        return this.giftDetection(record);
      })
      .then(record => {
        return this.stockDetection(record);
      });
  }

  duplicateDetection(params) {
    const { orderList, record } = this;
    const { kzSkuCode } = record;
    const exist = orderList.find(item => item.kzSkuCode === kzSkuCode);
    return new Promise((resolve, reject) => {
      if (exist) {
        Dialog.confirm({
          title: '提示',
          messageProps: { type: 'warning' },
          content: '该商品已加入销售清单，需要覆盖还是叠加？',
          okProps: { children: '覆盖' },
          cancelProps: { children: '叠加', type: 'primary' },
          onOk: () => {
            resolve({ ...params });
          },
          onCancel: () => {
            this.record.amount = exist.amount + params.amount;
            resolve({ ...params, amount: exist.amount + params.amount });
          },
        });
      } else {
        resolve(params);
      }
    });
  }

  giftDetection(params) {
   // ...
  }

  stockDetection(params) {
   // ...
  }
}
```

详细的demo和代码可以看这个codepen：

[codepen](https://codepen.io/silkshadow/embed/ZEeMKvN)

类似于这样的需求还是比较普遍的，比如在提交销售订单时，会有这样一些前置的判断：

+ 是否需要进行特价申请
+ 是否需要拆单
+ 是否需要进行赠品申请

其实用上述的职责链模式，把每一个判断当作一个任务，做链式调用，整个方法会很清晰。

两个实际项目的例子，给大家一个参考。

---

参考：

+ [JavaScript Chain of Responsibility pattern with Promises](https://bumbu.me/javascript-chain-of-responsibility-pattern-with-promises/)
