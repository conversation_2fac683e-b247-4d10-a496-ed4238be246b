---
author: silkshadow
pubDatetime: 2023-03-31T15:20:35Z
modDatetime: 2023-03-31T16:46:34.155Z
title: VSCode 插件开发实践
featured: false
draft: false
tags:
- vscode
description:
  从脚手架开始讲解最基础的demo，再写做一个插件实践。
---

> 从脚手架开始讲解最基础的demo，再写做一个插件实践。
>

<h3 id="j4QLB">快速开始</h3>

初始化使用 [官方的脚手架](https://code.visualstudio.com/api/get-started/your-first-extension) 生成：


```shell
# 安装脚手架
npm install -g yo generator-code

# 使用脚手架创建项目
yo code
```



目录结构：

```bash
.
├── .vscode # vscode调试配置
|  ├── extensions.json
|  ├── launch.json
|  ├── settings.json
|  └── tasks.json
├── src # 核心源码内容
|  ├── extension.ts # 入口文件
|  └── test # 测试文件
├── .eslintrc.json # eslint配置
├── .vscodeignore  # 类似 .npmignore，发布忽略内容
├── CHANGELOG.md 	 # 修改日志
├── README.md 		 # 插件发布后，插件主页内容
├── package.json 	 # 插件配置信息
├── tsconfig.json  # typescript配置
└─── vsc-extension-quickstart.md 
```

<h3 id="SzrpY">开发流程</h3>

![画板](https://cdn.nlark.com/yuque/0/2023/jpeg/112238/1680080766665-f88cda0c-fdbb-416e-b854-ce77e7d34b74.jpeg)

<h4 id="VjMEY">插件入口</h4>
我们来看一下 `src/extension.ts` 

```javascript
// vscode 模块不需要安装，由插件运行时注入
import * as vscode from 'vscode';

// 插件加载时执行的 activate 钩子方法
export function activate(context: vscode.ExtensionContext) {

  console.log('Congratulations, your extension "hello-world" is now active!');

  // 注册一个命令，返回 vscode.Disposable 对象，该对象包含 dispose 销毁方法
  let disposable = vscode.commands.registerCommand('hello-world.helloWorld', () => {
    // 弹出一个信息框消息
    vscode.window.showInformationMessage('Hello World from hello-world!');
  });

  // context 订阅注册事件
  context.subscriptions.push(disposable);
}

// 插件被用户卸载时调用的钩子
export function deactivate() {}

```

【调试】点击 『Run Extension』会启动一个 [Extension Development Host] 窗口，这个窗口会加载我们的插件

![](https://cdn.nlark.com/yuque/0/2023/png/112238/1678677119132-f0835120-a4f4-4528-a05c-d687fbc80143.png)

脚手架里插件默认是输入 『Hello World』然后右下角弹窗

![](https://cdn.nlark.com/yuque/0/2023/png/112238/1678677289252-32a92814-102f-4ee4-bd2c-6c555bbde7f2.png)

至此，一个 VSCode 插件的初始化就完成啦 ~



<h4 id="fLfzc">插件配置</h4>
VSCode 开发配置复用了 npm 包特性，详见 [Fields](https://code.visualstudio.com/api/references/extension-manifest#fields)，但有几个比较重要的属性：

+ `main` 就是插件入口，实际上就是 `src/extension.ts` 编译出来的产物
+ `contributes` 可以理解成 <font style="background-color:#E8F7CF;">功能声明清单</font>，插件有关的**命令、配置、UI、snippets** 等都需要这个字段

```json
{
  "name": "hello-world",
  "displayName": "hello-world",
  "description": "",
  "version": "0.0.1",
  "engines": {
    "vscode": "^1.49.0"
  },
  "categories": [
    "Other"
  ],
  "activationEvents": [
    "onCommand:hello-world.helloWorld"
  ],
  "main": "./out/extension.js",
  "contributes": {
    "commands": [
      {
        "command": "hello-world.helloWorld",
        "title": "Hello World"
      }
    ]
  },
  "scripts": {
    "vscode:prepublish": "yarn run compile",
    "compile": "tsc -p ./",
    "lint": "eslint src --ext ts",
    "watch": "tsc -watch -p ./",
    "pretest": "yarn run compile && yarn run lint",
    "test": "node ./out/test/runTest.js"
  },
  "devDependencies": {}
}

```



<h4 id="yBgCx">打包发布</h4>
此部分在开发实践部分中展开。

<h3 id="In4mb">开发实践</h3>
:::color5
前端业务开发，新建页面，往往都是相同的模式：创建页面、样式、请求等文件，且文件初始化格式也是相似的，这部分工作有些繁琐，因此我的一个需求时能够一键创建这些基础文件。

此外，我们的基础UI框架也是统一的，即使用了3年fusion.design组件库，每次写页面时依然要查阅文档，因此我的另一个需求时快捷创建组件代码demo片段。

:::



> 开发这个插件时，正在开始投入开发电商小程序，使用的是taro + taro-ui，创建的页面和组件都是基于这个框架的。
>

<h4 id="lq72M">一键创建基础文件</h4>
<h5 id="EDDx2">命令</h5>
入口文件注册命令：

```typescript
export function activate(context: vscode.ExtensionContext) {
  const file = new FileGenerator();

  let createTaroPage = vscode.commands.registerCommand(
    // 注册createTaroPage命令
    'extension.createTaroPage',
    (uri: vscode.Uri) => {
      // 执行文件创建方法
      file.execute(uri);
      vscode.window.showInformationMessage('createTaroPage');
    }
  );

  context.subscriptions.push(createTaroPage);
}

export function deactivate() {}
```

> 备注：FileGenerator方法为创建文件方法，不涉及VSCode 插件语法，暂省略。
>

<h5 id="fMSOK">配置</h5>
`vscode.commands.registerCommand`仅仅是将命令id绑定到了处理函数上，如果想让用户<font style="background-color:#D9DFFC;">从命令面板中找到你的命令</font>，你还需要在package.json中配置对应的**命令配置项**(contribution)：

```json
{
  "contributes": {
    "commands": [
       {
        "command": "extension.createTaroPage",
        "title": "%cmd.createTaroPage.title%"
      }
    ]
  }
}
```

并注册一个面向全部用户场景的命令onCommand activiationEvent：

```json
{
  "activationEvents": ["onCommand:extension.createTaroPage"]
}
```

此时我们已经可以在控制面板上执行命令。但是为了更友好的交互，我希望直接在对应文件夹右键一键执行命令，那么可以在 `contributes.menus` 下配置运行你限制命令出现在命令面板的时机。

```json
{
	"contributes": {
    "menus": {
			"explorer/context": [
				{
					"command": "extension.createTaroPage",
					"when": "explorerResourceIsFolder",
					"group": "navigation"
				}
			]
		},
  }
}
```

+ `explorer/context`：资源管理器上下文菜单
+ `command`：执行的命令
+ `when`：显示这个菜单项的时机
+ `group`：菜单分组，设置 navigation 就会总是显示在菜单的最顶端



![](https://cdn.nlark.com/yuque/0/2023/png/112238/1680229439077-c2d1c36e-d78a-4740-b66f-5ccd70eb1a6d.png)





<h4 id="RdCq2">快捷创建组件代码</h4>
<h5 id="gKg25">Snippe</h5>
snippet 也是通过编写配置文件来定义的，大概内容就是这样：

```json
{
  "AtButton": {
    "prefix": "taro-button",
    "body": [
      "<AtAvatar circle image='https://jdc.jd.com/img/200'></AtAvatar>"
    ],
    "description": "按钮"
  },
}
```

+ `AtButton` 是 snippet 的名字，会在详细信息中展示出来。
+ `prefix` 是 触发的字符，可以是一个列表，也可以是一个字符串。用户输入的字符如果是 prefixes 的子串的话（<font style="color:rgb(68, 68, 68);">Substring matching</font>），这个 snippet 会被触发。

![](https://cdn.nlark.com/yuque/0/2023/png/112238/1680229184932-c20c614b-99da-4c8b-901b-fbc2eaaa280d.png)

+ `body` 里定义的就是要被写入的内容，可以使用 $0 $1 来进行光标占位，然后可以用 tab 切换位置。也可以使用 ${1:defaultText} 来设置默认文本。具体占位规则还有很多丰富的格式，可以[在这查看](https://code.visualstudio.com/docs/editor/userdefinedsnippets#_snippet-syntax)。

<h5 id="VDQjS">配置</h5>
+ language：属性必须是语言标识符
+ path：使用代码片段文件的相对路径

```json
{
	"contributes": {
    "snippets": [
			{
        "language": "javascript",
        "path": "./snippets/snippets.json"
      },
      {
        "language": "javascriptreact",
        "path": "./snippets/snippets.json"
      },
      {
        "language": "typescript",
        "path": "./snippets/snippets.json"
      },
      {
        "language": "typescriptreact",
        "path": "./snippets/snippets.json"
      }
		]
  }
}
```

<h4 id="hc6MA">打包发布</h4>
<h5 id="gmmoi">打包</h5>
运行 webpack 进行打包，脚手架已经有预设的命令：

```bash
{
	"scripts": {
		"vscode:prepublish": "yarn run package",
		"compile": "webpack",
		"watch": "webpack --watch",
		"package": "webpack --mode production --devtool hidden-source-map",
	}
}
```

```bash
npm run vscode:prepublish
```

<h5 id="itiML">发布</h5>
直接用官方的 [vsce](https://github.com/microsoft/vscode-vsce) 工具：

```shell
# 安装打包工具vsce
npm i vsce -g
# vsce将在打包后的vsix文件保存在项目根目录中
vsce package
```



部署上线前需要注册 Azure 账号，具体步骤可以按[官方文档](https://code.visualstudio.com/api/working-with-extensions/publishing-extension)操作。

1. 在[https://marketplace.visualstudio.com/vscode](https://marketplace.visualstudio.com/vscode)登陆微软账号

![](https://cdn.nlark.com/yuque/0/2023/png/112238/1678759581917-2ec3fc4e-967e-4b7f-8bec-0ab87323b6b2.png)

2. 完成相关注册等

![](https://cdn.nlark.com/yuque/0/2023/png/112238/1678759581589-3bf70ac8-de55-44e5-bffa-f5da7f623a54.png)

3. 将打包好的vsix文件上传到这里即可

![](https://cdn.nlark.com/yuque/0/2023/png/112238/1678759581586-9c67d741-f665-4354-9847-3a163e43a60f.png)

<h3 id="eGOlG">小结</h3>
vscode插件能力还是很强大的，常用的能力如下：

+ 利用node能力访问本地存储
+ 利用编辑器API自定义命令、快捷键、菜单
+ 自定义跳转、补全、提示、高亮等语言功能
+ 自定义图标、主题等
+ 自定义左侧功能面板
+ 状态栏、通知、git、Debuger等等

我们的实践中，用到了利用node能力访问本地存储能力、利用编辑器API自定义命令以及菜单、自定义补全功能。

推荐阅读《[VSCode 语言插件开发入门](https://yuque.antfin-inc.com/lijiacheng.ljc/creature/gnwgx7#Kl7ni)》深入学习vscode插件的其他能力及实现原理。

<h4 id="DoTv0"></h4>


