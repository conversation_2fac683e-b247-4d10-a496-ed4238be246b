---
author: silkshadow
pubDatetime: 2022-10-07T15:20:35Z
modDatetime: 2022-10-07T16:46:34.155Z
title: 长列表渲染优化记录
featured: false
draft: false
tags:
- vscode
description:
  长列表渲染优化记录
---

<h2 id="m9T68"><font style="color:rgb(31, 35, 40);">业务场景</font></h2>
客户详情页，点击地址信息模块的[编辑]按钮，所有地址变成可编辑状态，并展示对应地址地图，点击[取消]按钮返回只读状态。由于地址列表不分页，一次性渲染大量地图对页面性能来说是个挑战。



<h2 id="FoQ8N">尝试优化1 —— 虚拟滚动</h2>
> **<font style="color:rgb(31, 35, 40);">根据滚动容器元素的可视区域来渲染长列表数据中某一个部分数据。</font>**
>

****

**原理：**在处理用户滚动时，要改变列表在可视区域的渲染部分

+ 计算当前可见区域起始数据的 startIndex
+ 计算当前可见区域结束数据的 endIndex
+ 计算当前可见区域的数据，并渲染到页面中
+ 计算 startIndex 对应的数据在整个列表中的偏移位置 startOffset，并设置到列表上
+ 计算 endIndex 对应的数据相对于可滚动区域最底部的偏移位置 endOffset，并设置到列表上

startOffset 和 endOffset 会撑开容器元素的内容高度，让其可持续的滚动；此外，还能保持滚动条处于一个正确的位置。



![](https://cdn.nlark.com/yuque/0/2023/png/112238/1692861630313-1c48f5c6-a2c9-40db-8965-8fb5e214aa23.png)  


基于ahooks工具库实现的伪代码：<font style="color:#585A5A;">（3.x版本与2.x版本API略有不同，前置仓安装的是2.x版本）</font>

```jsx
import { useVirtualList } from 'ahooks';

export default () => {
  const { list, containerProps, wrapperProps } = useVirtualList(logisticsAddress), {
    overscan: 30,
    itemHeight: 60,
    });

  return (
    <>
      <div {...containerProps} style={{ height: '700px', overflow: 'auto' }}>
        <div {...wrapperProps}>
          {list.map((ele) => (
            <Address />
          ))}
        </div>
      </div>
    </>
  );
};
```



**问题：**

可视区域滚动到的子组件重复渲染，滚动后编辑状态不可保留。



<h2 id="QYc7I">尝试优化2 —— 懒加载</h2>
> **进入可视区域内才进行加载，减少可视区域(首屏)的地图资源加载数。**
>



**原理：**

基于 [IntersectionObserver](https://developer.mozilla.org/zh-CN/docs/Web/API/IntersectionObserver) API，判断元素是否进入"视口"（viewport）

![](https://cdn.nlark.com/yuque/0/2023/png/112238/1692865529573-5d624cc7-b88a-4eb1-b87e-d1e7fc9fbaea.png)

+ `boundingClientRect`：目标元素的矩形区域的信息
+ `intersectionRect`：目标元素与视口（或根元素）的交叉区域的信息
+ `intersectionRatio`：目标元素的可见比例，即intersectionRect占boundingClientRect的比例，完全可见时为1，完全不可见时小于等于0
+ `isIntersecting`：返回一个布尔值，如果目标元素与交叉区域观察者对象 (intersection observer) 的根相交，则返回 true 。

```jsx
const observer = new IntersectionObserver(
  (entries) => {
    for (const entry of entries) {
      setRatio(entry.intersectionRatio);
      setState(entry.isIntersecting); // 可以通过这个值判断是否可见
    }
  },
  {
    ...option,
    root: getTargetElement(options?.root),
  },
);

els.forEach((el) => {
  if (el) {
    observer.observe(el);
  }
});
```



**伪代码：**

```jsx
import { useInViewport } from 'ahooks';

const LazyChildren = props => {
  const { height } = props;
  const ref = useRef();
  const inViewport = useInViewport(ref);

  return <div ref={ref}>
    {inViewport ? props.children : <div style={{ height }}><Loading /></div>}
  </div>;
};

```

```tsx
import { useInViewport } from 'ahooks';
import React, { useRef, useMemo, useEffect } from 'react';
import { Subtract } from 'utility-types';

interface LazyChildrenProps {
  height: number;
}
type InjectProps = {};

const LazyChildren = <P extends InjectProps>(Component: React.ComponentType<P>) => {
  const hocComponent = ({ ...props }: Subtract<P, InjectProps> & LazyChildrenProps) => {
  const { height, ...componentProps } = props;
    const hasRendered = useRef(false);
    const ref = useRef();
    const inViewport = useInViewport(ref);
  
    useEffect(() => {
      hasRendered.current = false;
    }, [componentProps]);
  
    useEffect(() => {
      if (inViewport) hasRendered.current = true;
    }, [inViewport]);
  
    return <div ref={ref}>{inViewport || hasRendered.current ? <Component {...(componentProps as P)} /> : <div style={{ height }}><Loading /></div>}</div>;
    };
    return hocComponent;
  };

  export default LazyChildren;
```

```jsx
const LazyAddress = LazyChildren(Address);

export default () => {
  return (
    <>
      {detail.logisticsAddress.map((address, index) => {
        return <LazyAddress height={editState ? 560 : 145}/>        
      })}
    </>
  );
};
```



**小结：**

解决了虚拟滚动状态不保留的问题，但是由于即将进入视窗才进行渲染，可能导致滚动过快出现白屏，所以默认未出现在视窗前使用Loading展示。





<font style="color:rgb(0, 0, 0);"></font>

