// Personal information configuration
// Update this file with your own information

export const PERSONAL_INFO = {
  // Basic Information
  name: "Your Name",
  title: "A Full-stack Developer",
  email: "<EMAIL>",
  location: "Your City, Country",
  
  // Hero Section
  hero: {
    greeting: "Hi my name is",
    taglines: [
      "PASSIONATE PROGRAMMER",
      "FREELANCER", 
      "FULL-STACK DEVELOPER"
    ],
    description: `I am a Full-Stack Developer with a passion for delivering exceptional results.
    With my expertise in modern web technologies, I bring a unique combination of 
    technical skills and creative problem-solving to every project I work on.`,
    ctaText: "Contact me!",
    learnMoreText: "Learn More"
  },

  // About Section
  about: {
    title: "Who am I?",
    description: `With 4+ years of comprehensive experience in web application development, 
    I have polished my skills in both frontend and backend development. 
    In addition to my hands-on experience in web development, my education 
    has also played a critical role in providing a strong foundation for my career.`,
    
    education: [
      {
        degree: "B.Sc (Hons) in Computing",
        institution: "Your University",
        period: "2018 ~ 2019",
        highlights: [
          "Studied computer science, software development, DevOps",
          "Graduated with First Class Honours",
          "Got merit in 7 modules out of 9"
        ]
      },
      {
        degree: "HND in Computing & System Development", 
        institution: "Your College",
        period: "2016 - 2018",
        highlights: [
          "Studied modules specializing in software development",
          "Passed HND with overall Merit"
        ]
      },
      {
        degree: "Professional Certifications",
        institution: "Various Institutions",
        period: "2017 - Present",
        highlights: [
          "Modern web development certifications",
          "Cloud computing and DevOps certifications"
        ]
      }
    ],

    technologies: [
      "React", "TypeScript", "Node.js", "Astro", "TailwindCSS", "Python"
    ]
  },

  // Contact Section
  contact: {
    title: "Contact",
    subtitle: "Let's be awesome together!",
    description: `As a dev, I am driven by my love for coding and my desire for new challenges. 
    If you have opportunities for collaboration or want to build something amazing, 
    don't hesitate to contact me!`,
    
    methods: [
      {
        type: "email",
        title: "Email Me",
        description: "Send me an email and I'll get back to you as soon as possible.",
        action: "Get in touch!"
      },
      {
        type: "social",
        title: "Follow Me", 
        description: "Connect with me on social media for updates and tech discussions.",
        action: "Connect"
      }
    ],

    features: [
      "Quick Response",
      "Remote Friendly", 
      "Professional"
    ]
  },

  // Skills & Technologies
  skills: {
    frontend: ["React", "Vue.js", "TypeScript", "TailwindCSS", "Astro"],
    backend: ["Node.js", "Python", "Express", "FastAPI", "PostgreSQL"],
    tools: ["Git", "Docker", "AWS", "Vercel", "Figma"],
    mobile: ["React Native", "Expo"]
  }
} as const;

// Helper functions
export const getFullName = () => PERSONAL_INFO.name;
export const getEmail = () => PERSONAL_INFO.email;
export const getTitle = () => PERSONAL_INFO.title;
export const getHeroTaglines = () => PERSONAL_INFO.hero.taglines;
export const getTechnologies = () => PERSONAL_INFO.about.technologies;
export const getEducation = () => PERSONAL_INFO.about.education;
