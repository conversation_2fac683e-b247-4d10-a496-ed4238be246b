---
import LinkButton from "@/components/LinkButton.astro";
import { PERSONAL_INFO } from "@/data/personal";
---

<section id="hero" class="relative min-h-screen flex items-center justify-center overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute inset-0 -z-10">
    <div class="absolute top-20 left-10 w-32 h-32 bg-accent/10 rounded-full blur-xl"></div>
    <div class="absolute bottom-20 right-10 w-48 h-48 bg-accent/5 rounded-full blur-2xl"></div>
  </div>

  <div class="container mx-auto px-4 py-20">
    <div class="grid lg:grid-cols-2 gap-12 items-center">
      <!-- Left content -->
      <div class="space-y-8">
        <!-- Tags -->
        <div class="flex flex-wrap gap-2 text-sm font-medium">
          {PERSONAL_INFO.hero.taglines.map((tagline) => (
            <span class="px-3 py-1 bg-accent/10 text-accent rounded-full">{tagline}</span>
          ))}
        </div>

        <!-- Main heading -->
        <div class="space-y-4">
          <p class="text-lg text-skin-muted animate-fade-in-up">{PERSONAL_INFO.hero.greeting}</p>
          <h1 class="text-5xl lg:text-7xl font-bold gradient-text leading-tight animate-fade-in-up">
            {PERSONAL_INFO.name}
          </h1>
          <h2 class="text-2xl lg:text-3xl font-semibold text-skin-base animate-fade-in-up">
            {PERSONAL_INFO.title}
          </h2>
        </div>

        <!-- Description -->
        <p class="text-lg text-skin-muted leading-relaxed max-w-lg">
          {PERSONAL_INFO.hero.description}
        </p>

        <!-- CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4">
          <LinkButton
            href="#contact"
            class="inline-flex items-center justify-center px-8 py-3 bg-accent text-white font-semibold rounded-lg hover:bg-accent/90 transition-colors"
          >
            {PERSONAL_INFO.hero.ctaText}
          </LinkButton>
          <LinkButton
            href="#about"
            class="inline-flex items-center justify-center px-8 py-3 border-2 border-accent text-accent font-semibold rounded-lg hover:bg-accent hover:text-white transition-colors"
          >
            {PERSONAL_INFO.hero.learnMoreText}
          </LinkButton>
        </div>

        <!-- Scroll indicator -->
        <div class="hidden lg:block pt-8">
          <a href="#about" class="inline-flex flex-col items-center text-skin-muted hover:text-accent transition-colors">
            <span class="text-sm mb-2">Scroll</span>
            <svg class="w-6 h-6 animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
            </svg>
          </a>
        </div>
      </div>

      <!-- Right content - Illustration/Image -->
      <div class="relative">
        <div class="relative z-10">
          <!-- Placeholder for character illustration -->
          <div class="w-full max-w-md mx-auto aspect-square bg-gradient-to-br from-accent/20 to-accent/5 rounded-full flex items-center justify-center">
            <div class="w-3/4 h-3/4 bg-gradient-to-br from-accent/30 to-accent/10 rounded-full flex items-center justify-center">
              <svg class="w-32 h-32 text-accent/60" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 7.5V9M15 11.5C15.8 11.5 16.5 12.2 16.5 13S15.8 14.5 15 14.5 13.5 13.8 13.5 13 14.2 11.5 15 11.5M5 12C5.8 12 6.5 12.7 6.5 13.5S5.8 15 5 15 3.5 14.3 3.5 13.5 4.2 12 5 12M12 7.5C12.8 7.5 13.5 8.2 13.5 9S12.8 10.5 12 10.5 10.5 9.8 10.5 9 11.2 7.5 12 7.5M12 20C12.8 20 13.5 20.7 13.5 21.5S12.8 23 12 23 10.5 22.3 10.5 21.5 11.2 20 12 20Z"/>
              </svg>
            </div>
          </div>
        </div>
        
        <!-- Floating laptop illustration -->
        <div class="absolute -bottom-10 -right-10 w-48 h-32 bg-gradient-to-br from-skin-card to-skin-card/80 rounded-lg shadow-xl transform rotate-12 hidden lg:block">
          <div class="w-full h-full bg-skin-inverted/5 rounded-lg flex items-center justify-center">
            <svg class="w-16 h-16 text-accent/40" fill="currentColor" viewBox="0 0 24 24">
              <path d="M20,18C20.5,18 21,18.4 21,19V20C21,20.5 20.5,21 20,21H4C3.4,21 3,20.5 3,20V19C3,18.4 3.4,18 4,18H20M20,16H4L2,4H22L20,16Z"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
