---
import { PERSONAL_INFO } from "@/data/personal";
---

<section id="about" class="py-20 bg-skin-card/30">
  <div class="container mx-auto px-4">
    <div class="grid lg:grid-cols-2 gap-16 items-center">
      <!-- Left content - Image -->
      <div class="relative">
        <div class="relative z-10">
          <!-- Profile image placeholder -->
          <div class="w-full max-w-sm mx-auto aspect-square bg-gradient-to-br from-accent/20 to-accent/5 rounded-2xl overflow-hidden">
            <div class="w-full h-full bg-gradient-to-br from-accent/30 to-accent/10 flex items-center justify-center">
              <svg class="w-32 h-32 text-accent/60" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"/>
              </svg>
            </div>
          </div>
        </div>
        
        <!-- Background decorative dots -->
        <div class="absolute -top-8 -left-8 w-24 h-24 opacity-20">
          <svg viewBox="0 0 100 100" class="w-full h-full text-accent">
            <defs>
              <pattern id="dots" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                <circle cx="10" cy="10" r="2" fill="currentColor"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#dots)"/>
          </svg>
        </div>
      </div>

      <!-- Right content - Text -->
      <div class="space-y-8">
        <div>
          <h2 class="text-4xl lg:text-5xl font-bold text-skin-accent mb-6">
            {PERSONAL_INFO.about.title}
          </h2>
          <p class="text-lg text-skin-muted leading-relaxed">
            {PERSONAL_INFO.about.description}
          </p>
        </div>

        <div class="space-y-6">
          <p class="text-skin-base">Here is my educational background.</p>
          
          <!-- Education items -->
          <div class="space-y-6">
            {PERSONAL_INFO.about.education.map((edu) => (
              <div class="border-l-4 border-accent pl-6 py-2">
                <h3 class="text-xl font-semibold text-skin-accent mb-2">
                  {edu.degree}
                </h3>
                <p class="text-accent font-medium mb-2">
                  {edu.institution} | {edu.period}
                </p>
                <ul class="text-skin-muted space-y-1">
                  {edu.highlights.map((highlight) => (
                    <li>• {highlight}</li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        <!-- Skills or technologies -->
        <div class="pt-4">
          <h4 class="text-lg font-semibold text-skin-accent mb-4">Technologies I work with:</h4>
          <div class="flex flex-wrap gap-2">
            {PERSONAL_INFO.about.technologies.map((tech) => (
              <span class="px-3 py-1 bg-accent/10 text-accent rounded-full text-sm">{tech}</span>
            ))}
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
