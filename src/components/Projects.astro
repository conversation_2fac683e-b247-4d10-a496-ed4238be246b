---
import { projects, getFeaturedProjects } from "@/data/projects";

const featuredProjects = getFeaturedProjects();
const otherProjects = projects.filter(project => !project.featured);
---

<section id="projects" class="py-20">
  <div class="container mx-auto px-4">
    <!-- Section header -->
    <div class="text-center mb-16">
      <h2 class="text-4xl lg:text-5xl font-bold text-skin-accent mb-6">
        Featured Projects
      </h2>
      <p class="text-lg text-skin-muted max-w-2xl mx-auto">
        "Talk is cheap. Show me the code"? I got you.<br>
        Here are some of my projects you shouldn't miss
      </p>
    </div>

    <!-- Featured projects grid -->
    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
      {featuredProjects.map((project) => (
        <div class="group bg-skin-card rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-2">
          <!-- Project image placeholder -->
          <div class="aspect-video bg-gradient-to-br from-accent/20 to-accent/5 flex items-center justify-center">
            <div class="w-16 h-16 bg-accent/20 rounded-lg flex items-center justify-center">
              <svg class="w-8 h-8 text-accent" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,2A10,10 0 0,0 2,12C2,16.42 4.87,20.17 8.84,21.5C9.34,21.58 9.5,21.27 9.5,21C9.5,20.77 9.5,20.14 9.5,19.31C6.73,19.91 6.14,17.97 6.14,17.97C5.68,16.81 5.03,16.5 5.03,16.5C4.12,15.88 5.1,15.9 5.1,15.9C6.1,15.97 6.63,16.93 6.63,16.93C7.5,18.45 8.97,18 9.54,17.76C9.63,17.11 9.89,16.67 10.17,16.42C7.95,16.17 5.62,15.31 5.62,11.5C5.62,10.39 6,9.5 6.65,8.79C6.55,8.54 6.2,7.5 6.75,6.15C6.75,6.15 7.59,5.88 9.5,7.17C10.29,6.95 11.15,6.84 12,6.84C12.85,6.84 13.71,6.95 14.5,7.17C16.41,5.88 17.25,6.15 17.25,6.15C17.8,7.5 17.45,8.54 17.35,8.79C18,9.5 18.38,10.39 18.38,11.5C18.38,15.32 16.04,16.16 13.81,16.41C14.17,16.72 14.5,17.33 14.5,18.26C14.5,19.6 14.5,20.68 14.5,21C14.5,21.27 14.66,21.59 15.17,21.5C19.14,20.16 22,16.42 22,12A10,10 0 0,0 12,2Z"/>
              </svg>
            </div>
          </div>
          
          <!-- Project content -->
          <div class="p-6">
            <div class="flex items-start justify-between mb-3">
              <h3 class="text-xl font-semibold text-skin-accent group-hover:text-accent transition-colors">
                {project.title}
              </h3>
              <div class="flex gap-2">
                {project.githubUrl && (
                  <a 
                    href={project.githubUrl} 
                    class="text-skin-muted hover:text-accent transition-colors"
                    aria-label={`View ${project.title} on GitHub`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12,2A10,10 0 0,0 2,12C2,16.42 4.87,20.17 8.84,21.5C9.34,21.58 9.5,21.27 9.5,21C9.5,20.77 9.5,20.14 9.5,19.31C6.73,19.91 6.14,17.97 6.14,17.97C5.68,16.81 5.03,16.5 5.03,16.5C4.12,15.88 5.1,15.9 5.1,15.9C6.1,15.97 6.63,16.93 6.63,16.93C7.5,18.45 8.97,18 9.54,17.76C9.63,17.11 9.89,16.67 10.17,16.42C7.95,16.17 5.62,15.31 5.62,11.5C5.62,10.39 6,9.5 6.65,8.79C6.55,8.54 6.2,7.5 6.75,6.15C6.75,6.15 7.59,5.88 9.5,7.17C10.29,6.95 11.15,6.84 12,6.84C12.85,6.84 13.71,6.95 14.5,7.17C16.41,5.88 17.25,6.15 17.25,6.15C17.8,7.5 17.45,8.54 17.35,8.79C18,9.5 18.38,10.39 18.38,11.5C18.38,15.32 16.04,16.16 13.81,16.41C14.17,16.72 14.5,17.33 14.5,18.26C14.5,19.6 14.5,20.68 14.5,21C14.5,21.27 14.66,21.59 15.17,21.5C19.14,20.16 22,16.42 22,12A10,10 0 0,0 12,2Z"/>
                    </svg>
                  </a>
                )}
                {project.liveUrl && (
                  <a 
                    href={project.liveUrl} 
                    class="text-skin-muted hover:text-accent transition-colors"
                    aria-label={`View ${project.title} live demo`}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                    </svg>
                  </a>
                )}
              </div>
            </div>
            
            <p class="text-skin-muted mb-4 text-sm leading-relaxed">
              {project.description}
            </p>
            
            <!-- Technologies -->
            <div class="flex flex-wrap gap-2">
              {project.technologies.map((tech) => (
                <span class="px-2 py-1 bg-accent/10 text-accent rounded text-xs font-medium">
                  {tech}
                </span>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>

    <!-- Other projects -->
    {otherProjects.length > 0 && (
      <div>
        <h3 class="text-2xl font-semibold text-skin-accent mb-8 text-center">
          Other Notable Projects
        </h3>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {otherProjects.map((project) => (
            <div class="bg-skin-card rounded-lg p-6 hover:shadow-lg transition-shadow">
              <div class="flex items-start justify-between mb-3">
                <h4 class="text-lg font-semibold text-skin-accent">
                  {project.title}
                </h4>
                <div class="flex gap-2">
                  {project.githubUrl && (
                    <a 
                      href={project.githubUrl} 
                      class="text-skin-muted hover:text-accent transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12,2A10,10 0 0,0 2,12C2,16.42 4.87,20.17 8.84,21.5C9.34,21.58 9.5,21.27 9.5,21C9.5,20.77 9.5,20.14 9.5,19.31C6.73,19.91 6.14,17.97 6.14,17.97C5.68,16.81 5.03,16.5 5.03,16.5C4.12,15.88 5.1,15.9 5.1,15.9C6.1,15.97 6.63,16.93 6.63,16.93C7.5,18.45 8.97,18 9.54,17.76C9.63,17.11 9.89,16.67 10.17,16.42C7.95,16.17 5.62,15.31 5.62,11.5C5.62,10.39 6,9.5 6.65,8.79C6.55,8.54 6.2,7.5 6.75,6.15C6.75,6.15 7.59,5.88 9.5,7.17C10.29,6.95 11.15,6.84 12,6.84C12.85,6.84 13.71,6.95 14.5,7.17C16.41,5.88 17.25,6.15 17.25,6.15C17.8,7.5 17.45,8.54 17.35,8.79C18,9.5 18.38,10.39 18.38,11.5C18.38,15.32 16.04,16.16 13.81,16.41C14.17,16.72 14.5,17.33 14.5,18.26C14.5,19.6 14.5,20.68 14.5,21C14.5,21.27 14.66,21.59 15.17,21.5C19.14,20.16 22,16.42 22,12A10,10 0 0,0 12,2Z"/>
                      </svg>
                    </a>
                  )}
                  {project.liveUrl && (
                    <a 
                      href={project.liveUrl} 
                      class="text-skin-muted hover:text-accent transition-colors"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                      </svg>
                    </a>
                  )}
                </div>
              </div>
              
              <p class="text-skin-muted mb-4 text-sm">
                {project.description}
              </p>
              
              <div class="flex flex-wrap gap-1">
                {project.technologies.map((tech) => (
                  <span class="px-2 py-1 bg-accent/10 text-accent rounded text-xs">
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    )}

    <!-- View more projects link -->
    <div class="text-center mt-12">
      <p class="text-skin-muted mb-4">
        Other projects can be explored in 
        <a href="https://github.com" class="text-accent hover:underline font-medium" target="_blank" rel="noopener noreferrer">
          my github profile
        </a>
      </p>
    </div>
  </div>
</section>
