---
import Socials from "@/components/Socials.astro";
import LinkButton from "@/components/LinkButton.astro";
import { PERSONAL_INFO } from "@/data/personal";
---

<section id="contact" class="py-20 bg-skin-card/30">
  <div class="container mx-auto px-4">
    <div class="max-w-4xl mx-auto text-center">
      <!-- Section header -->
      <div class="mb-16">
        <h2 class="text-4xl lg:text-5xl font-bold text-skin-accent mb-6">
          {PERSONAL_INFO.contact.title}
        </h2>
        <div class="space-y-4">
          <h3 class="text-2xl lg:text-3xl font-semibold text-skin-base">
            {PERSONAL_INFO.contact.subtitle}
          </h3>
          <p class="text-lg text-skin-muted max-w-2xl mx-auto leading-relaxed">
            {PERSONAL_INFO.contact.description}
          </p>
        </div>
      </div>

      <!-- Contact methods -->
      <div class="grid md:grid-cols-2 gap-8 mb-12">
        <!-- Email contact -->
        <div class="bg-skin-card rounded-xl p-8 hover:shadow-lg transition-shadow">
          <div class="mb-6">
            <div class="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
              </svg>
            </div>
            <h4 class="text-xl font-semibold text-skin-accent mb-2">Email Me</h4>
            <p class="text-skin-muted mb-4">
              Send me an email and I'll get back to you as soon as possible.
            </p>
          </div>
          <LinkButton
            href={`mailto:${PERSONAL_INFO.email}`}
            class="inline-flex items-center justify-center px-6 py-3 bg-accent text-white font-semibold rounded-lg hover:bg-accent/90 transition-colors"
          >
            {PERSONAL_INFO.contact.methods[0].action}
          </LinkButton>
        </div>

        <!-- Social contact -->
        <div class="bg-skin-card rounded-xl p-8 hover:shadow-lg transition-shadow">
          <div class="mb-6">
            <div class="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-accent" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M7.07,18.28C7.5,17.38 10.12,16.5 12,16.5C13.88,16.5 16.5,17.38 16.93,18.28C15.57,19.36 13.86,20 12,20C10.14,20 8.43,19.36 7.07,18.28M18.36,16.83C16.93,15.09 13.46,14.5 12,14.5C10.54,14.5 7.07,15.09 5.64,16.83C4.62,15.5 4,13.82 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,13.82 19.38,15.5 18.36,16.83M12,6C10.06,6 8.5,7.56 8.5,9.5C8.5,11.44 10.06,13 12,13C13.94,13 15.5,11.44 15.5,9.5C15.5,7.56 13.94,6 12,6M12,11A1.5,1.5 0 0,1 10.5,9.5A1.5,1.5 0 0,1 12,8A1.5,1.5 0 0,1 13.5,9.5A1.5,1.5 0 0,1 12,11Z"/>
              </svg>
            </div>
            <h4 class="text-xl font-semibold text-skin-accent mb-2">Follow Me</h4>
            <p class="text-skin-muted mb-4">
              Connect with me on social media for updates and tech discussions.
            </p>
          </div>
          <div class="flex justify-center">
            <Socials />
          </div>
        </div>
      </div>

      <!-- Additional contact info -->
      <div class="bg-skin-card rounded-xl p-8">
        <h4 class="text-xl font-semibold text-skin-accent mb-4">
          Let's Work Together
        </h4>
        <p class="text-skin-muted mb-6 max-w-2xl mx-auto">
          I'm always interested in new opportunities and exciting projects. 
          Whether you're a company looking to hire, or you're a fellow developer 
          wanting to collaborate, I'd love to hear from you.
        </p>
        
        <!-- Contact options -->
        <div class="grid sm:grid-cols-3 gap-4 text-sm">
          {PERSONAL_INFO.contact.features.map((feature, index) => (
            <div class="flex items-center justify-center gap-2 text-skin-muted">
              {index === 0 && (
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              )}
              {index === 1 && (
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9v-9m0-9v9"/>
                </svg>
              )}
              {index === 2 && (
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
              )}
              <span>{feature}</span>
            </div>
          ))}
        </div>
      </div>

      <!-- Back to top -->
      <div class="mt-12">
        <a 
          href="#hero" 
          class="inline-flex items-center gap-2 text-skin-muted hover:text-accent transition-colors"
          aria-label="Back to top"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
          </svg>
          <span>Back to top</span>
        </a>
      </div>
    </div>
  </div>
</section>
