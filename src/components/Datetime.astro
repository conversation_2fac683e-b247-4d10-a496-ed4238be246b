---
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";
import IconCalendar from "@/assets/icons/IconCalendar.svg";
import { SITE } from "@/config";

dayjs.extend(utc);
dayjs.extend(timezone);

export interface Props {
  class?: string;
  size?: "sm" | "lg";
  timezone: string | undefined;
  pubDatetime: string | Date;
  modDatetime: string | Date | undefined | null;
}

const {
  pubDatetime,
  modDatetime,
  size = "sm",
  class: className = "",
  timezone: postTimezone,
} = Astro.props;

/* ========== Formatted Datetime ========== */
const isModified = modDatetime && modDatetime > pubDatetime;

const datetime = dayjs(isModified ? modDatetime : pubDatetime).tz(
  postTimezone || SITE.timezone
);

const date = datetime.format("D MMM, YYYY"); // e.g., '22 Mar, 2025'
---

<div class:list={["flex items-center gap-x-2 opacity-80", className]}>
  <IconCalendar
    class:list={[
      "inline-block size-6 min-w-[1.375rem]",
      { "scale-90": size === "sm" },
    ]}
  />
  {
    isModified && (
      <span class:list={["text-sm", { "sm:text-base": size === "lg" }]}>
        Updated:
      </span>
    )
  }
  <time
    class:list={["text-sm", { "sm:text-base": size === "lg" }]}
    datetime={datetime.toISOString()}>{date}</time
  >
</div>
